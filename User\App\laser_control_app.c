#include "laser_control_app.h"
#include "laser_drawing_app.h"

/* 全局变量 */
static DrawingPattern_t current_pattern = PATTERN_NONE;
static bool manual_mode = false;

/**
 * @brief 激光控制初始化
 */
void LaserControl_Init(void)
{
    current_pattern = PATTERN_NONE;
    manual_mode = false;
    Uart_Printf(&huart1, "Laser Control System Ready\r\n");
    LaserControl_ShowStatus();
}

/**
 * @brief 激光控制任务
 */
void LaserControl_Task(void)
{
    // 这里可以添加按键检测等逻辑
    // 目前主要通过串口命令控制
}

/**
 * @brief 处理控制命令
 */
void LaserControl_ProcessCommand(LaserCommand_t cmd)
{
    switch(cmd)
    {
        case LASER_CMD_STOP:
            LaserDrawing_Stop();
            current_pattern = PATTERN_NONE;
            Uart_Printf(&huart1, "Drawing stopped\r\n");
            break;
            
        case LASER_CMD_HOME:
            LaserDrawing_Home();
            Uart_Printf(&huart1, "Moving to home position\r\n");
            break;
            
        case LASER_CMD_DRAW_TRIANGLE:
            LaserControl_DrawPattern(PATTERN_TRIANGLE);
            break;
            
        case LASER_CMD_DRAW_SQUARE:
            LaserControl_DrawPattern(PATTERN_SQUARE);
            break;
            
        case LASER_CMD_DRAW_STAR:
            LaserControl_DrawPattern(PATTERN_STAR);
            break;
            
        case LASER_CMD_MOVE_UP:
            LaserControl_ManualMove(0, 5);
            break;
            
        case LASER_CMD_MOVE_DOWN:
            LaserControl_ManualMove(0, -5);
            break;
            
        case LASER_CMD_MOVE_LEFT:
            LaserControl_ManualMove(-5, 0);
            break;
            
        case LASER_CMD_MOVE_RIGHT:
            LaserControl_ManualMove(5, 0);
            break;
            
        case LASER_CMD_LASER_ON:
            LaserDrawing_SetLaser(true);
            Uart_Printf(&huart1, "Laser ON\r\n");
            break;
            
        case LASER_CMD_LASER_OFF:
            LaserDrawing_SetLaser(false);
            Uart_Printf(&huart1, "Laser OFF\r\n");
            break;
            
        default:
            Uart_Printf(&huart1, "Unknown command\r\n");
            break;
    }
}

/**
 * @brief 处理串口命令
 */
void LaserControl_ProcessUartCommand(char* cmd_str)
{
    if(cmd_str == NULL) return;
    
    if(strcmp(cmd_str, "stop") == 0) {
        LaserControl_ProcessCommand(LASER_CMD_STOP);
    }
    else if(strcmp(cmd_str, "home") == 0) {
        LaserControl_ProcessCommand(LASER_CMD_HOME);
    }
    else if(strcmp(cmd_str, "triangle") == 0) {
        LaserControl_ProcessCommand(LASER_CMD_DRAW_TRIANGLE);
    }
    else if(strcmp(cmd_str, "square") == 0) {
        LaserControl_ProcessCommand(LASER_CMD_DRAW_SQUARE);
    }
    else if(strcmp(cmd_str, "star") == 0) {
        LaserControl_ProcessCommand(LASER_CMD_DRAW_STAR);
    }
    else if(strcmp(cmd_str, "up") == 0) {
        LaserControl_ProcessCommand(LASER_CMD_MOVE_UP);
    }
    else if(strcmp(cmd_str, "down") == 0) {
        LaserControl_ProcessCommand(LASER_CMD_MOVE_DOWN);
    }
    else if(strcmp(cmd_str, "left") == 0) {
        LaserControl_ProcessCommand(LASER_CMD_MOVE_LEFT);
    }
    else if(strcmp(cmd_str, "right") == 0) {
        LaserControl_ProcessCommand(LASER_CMD_MOVE_RIGHT);
    }
    else if(strcmp(cmd_str, "laser_on") == 0) {
        LaserControl_ProcessCommand(LASER_CMD_LASER_ON);
    }
    else if(strcmp(cmd_str, "laser_off") == 0) {
        LaserControl_ProcessCommand(LASER_CMD_LASER_OFF);
    }
    else if(strcmp(cmd_str, "status") == 0) {
        LaserControl_ShowStatus();
    }
    else if(strcmp(cmd_str, "help") == 0) {
        Uart_Printf(&huart1, "Available commands:\r\n");
        Uart_Printf(&huart1, "  stop - Stop drawing\r\n");
        Uart_Printf(&huart1, "  home - Go to home position\r\n");
        Uart_Printf(&huart1, "  triangle - Draw triangle\r\n");
        Uart_Printf(&huart1, "  square - Draw square\r\n");
        Uart_Printf(&huart1, "  star - Draw star\r\n");
        Uart_Printf(&huart1, "  up/down/left/right - Manual move\r\n");
        Uart_Printf(&huart1, "  laser_on/laser_off - Control laser\r\n");
        Uart_Printf(&huart1, "  status - Show current status\r\n");
        Uart_Printf(&huart1, "  help - Show this help\r\n");
    }
    else {
        Uart_Printf(&huart1, "Unknown command: %s\r\n", cmd_str);
        Uart_Printf(&huart1, "Type 'help' for available commands\r\n");
    }
}

/**
 * @brief 画指定图案
 */
void LaserControl_DrawPattern(DrawingPattern_t pattern)
{
    if(LaserDrawing_IsDrawing()) {
        Uart_Printf(&huart1, "Already drawing, please wait or stop first\r\n");
        return;
    }
    
    current_pattern = pattern;
    
    switch(pattern)
    {
        case PATTERN_TRIANGLE:
            LaserDrawing_StartPattern(triangle_pattern, TRIANGLE_PATTERN_SIZE);
            Uart_Printf(&huart1, "Drawing triangle...\r\n");
            break;

        case PATTERN_SQUARE:
            LaserDrawing_StartPattern(square_pattern, SQUARE_PATTERN_SIZE);
            Uart_Printf(&huart1, "Drawing square...\r\n");
            break;

        case PATTERN_STAR:
            LaserDrawing_StartPattern(star_pattern, STAR_PATTERN_SIZE);
            Uart_Printf(&huart1, "Drawing star...\r\n");
            break;
            
        default:
            Uart_Printf(&huart1, "Pattern not supported\r\n");
            break;
    }
}

/**
 * @brief 手动移动
 */
void LaserControl_ManualMove(float dx, float dy)
{
    if(LaserDrawing_IsDrawing()) {
        Uart_Printf(&huart1, "Cannot move while drawing\r\n");
        return;
    }
    
    GimbalState_t state = LaserDrawing_GetState();
    float new_x = state.current_pan + dx;
    float new_y = state.current_tilt + dy;
    
    LaserDrawing_MoveTo(new_x, new_y);
    Uart_Printf(&huart1, "Moving to (%.1f, %.1f)\r\n", new_x, new_y);
}

/**
 * @brief 显示当前状态
 */
void LaserControl_ShowStatus(void)
{
    GimbalState_t state = LaserDrawing_GetState();
    
    Uart_Printf(&huart1, "=== Laser Drawing Status ===\r\n");
    Uart_Printf(&huart1, "Position: (%.1f, %.1f)\r\n", state.current_pan, state.current_tilt);
    Uart_Printf(&huart1, "Laser: %s\r\n", state.laser_on ? "ON" : "OFF");
    Uart_Printf(&huart1, "Drawing: %s\r\n", state.is_drawing ? "YES" : "NO");
    Uart_Printf(&huart1, "Speed: %d steps/s\r\n", state.speed);
    
    if(current_pattern != PATTERN_NONE) {
        const char* pattern_names[] = {"None", "Triangle", "Square", "Star", "Circle", "Custom"};
        Uart_Printf(&huart1, "Current Pattern: %s\r\n", pattern_names[current_pattern]);
    }
    
    Uart_Printf(&huart1, "===========================\r\n");
}
