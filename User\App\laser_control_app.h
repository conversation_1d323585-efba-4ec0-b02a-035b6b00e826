#ifndef __LASER_CONTROL_APP_H__
#define __LASER_CONTROL_APP_H__

#include "MyDefine.h"

/* 画图模式枚举 */
typedef enum {
    PATTERN_NONE = 0,
    PATTERN_TRIANGLE,
    PATTERN_SQUARE,
    PATTERN_STAR,
    PATTERN_CIRCLE,
    PATTERN_CUSTOM
} DrawingPattern_t;

/* 控制命令枚举 */
typedef enum {
    LASER_CMD_STOP = 0,
    LASER_CMD_HOME,
    LASER_CMD_DRAW_TRIANGLE,
    LASER_CMD_DRAW_SQUARE,
    LASER_CMD_DRAW_STAR,
    LASER_CMD_MOVE_UP,
    LASER_CMD_MOVE_DOWN,
    LASER_CMD_MOVE_LEFT,
    LASER_CMD_MOVE_RIGHT,
    LASER_CMD_LASER_ON,
    LASER_CMD_LASER_OFF
} LaserCommand_t;

/* 函数声明 */
void LaserControl_Init(void);
void LaserControl_Task(void);
void LaserControl_ProcessCommand(LaserCommand_t cmd);
void LaserControl_ProcessUartCommand(char* cmd_str);
void LaserControl_DrawPattern(DrawingPattern_t pattern);
void LaserControl_ManualMove(float dx, float dy);
void LaserControl_ShowStatus(void);

#endif
