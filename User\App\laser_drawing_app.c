#include "laser_drawing_app.h"
#include "stepper_driver.h"

/* 全局变量 */
static GimbalState_t gimbal_state;
static const DrawingInstruction_t* current_pattern = NULL;
static uint16_t pattern_size = 0;
static uint32_t last_step_time = 0;
static float target_pan = 0.0f;
static float target_tilt = 0.0f;
static bool moving_to_target = false;

/* 预定义图形 - 三角形 */
const DrawingInstruction_t triangle_pattern[] = {
    {CMD_LASER_OFF, {0, 0}, 0},
    {CMD_MOVE_TO, {-20, -15}, 0},
    {CMD_LASER_ON, {0, 0}, 0},
    {CMD_LINE_TO, {20, -15}, 0},
    {CMD_LINE_TO, {0, 15}, 0},
    {CMD_LINE_TO, {-20, -15}, 0},
    {CMD_LASER_OFF, {0, 0}, 0},
    {CMD_MOVE_TO, {0, 0}, 0}
};

/* 预定义图形 - 正方形 */
const DrawingInstruction_t square_pattern[] = {
    {CMD_LASER_OFF, {0, 0}, 0},
    {CMD_MOVE_TO, {-15, -15}, 0},
    {CMD_LASER_ON, {0, 0}, 0},
    {CMD_LINE_TO, {15, -15}, 0},
    {CMD_LINE_TO, {15, 15}, 0},
    {CMD_LINE_TO, {-15, 15}, 0},
    {CMD_LINE_TO, {-15, -15}, 0},
    {CMD_LASER_OFF, {0, 0}, 0},
    {CMD_MOVE_TO, {0, 0}, 0}
};

/* 预定义图形 - 五角星 */
const DrawingInstruction_t star_pattern[] = {
    {CMD_LASER_OFF, {0, 0}, 0},
    {CMD_MOVE_TO, {0, 20}, 0},
    {CMD_LASER_ON, {0, 0}, 0},
    {CMD_LINE_TO, {-12, -8}, 0},
    {CMD_LINE_TO, {19, 6}, 0},
    {CMD_LINE_TO, {-19, 6}, 0},
    {CMD_LINE_TO, {12, -8}, 0},
    {CMD_LINE_TO, {0, 20}, 0},
    {CMD_LASER_OFF, {0, 0}, 0},
    {CMD_MOVE_TO, {0, 0}, 0}
};

/**
 * @brief 激光画图系统初始化
 */
void LaserDrawing_Init(void)
{
    // 初始化步进电机驱动
    Stepper_Init();
    
    // 初始化云台状态
    gimbal_state.current_pan = 0.0f;
    gimbal_state.current_tilt = 0.0f;
    gimbal_state.laser_on = false;
    gimbal_state.speed = DRAWING_SPEED_DEFAULT;
    gimbal_state.is_drawing = false;
    gimbal_state.instruction_index = 0;
    
    // 初始化激光控制引脚
    LaserDrawing_SetLaser(false);
    
    // 回到原点
    LaserDrawing_Home();
    
    Uart_Printf(&huart1, "Laser Drawing System Initialized\r\n");
}

/**
 * @brief 激光画图任务 - 在调度器中定期调用
 */
void LaserDrawing_Task(void)
{
    if (!gimbal_state.is_drawing || current_pattern == NULL) {
        return;
    }
    
    // 检查是否完成当前移动
    if (moving_to_target) {
        if (Stepper_IsMoving()) {
            return; // 还在移动中
        }
        moving_to_target = false;
        
        // 更新当前位置
        gimbal_state.current_pan = target_pan;
        gimbal_state.current_tilt = target_tilt;
    }
    
    // 执行下一条指令
    if (gimbal_state.instruction_index < pattern_size) {
        const DrawingInstruction_t* instruction = &current_pattern[gimbal_state.instruction_index];
        
        switch (instruction->cmd) {
            case CMD_MOVE_TO:
                LaserDrawing_MoveTo(instruction->point.x, instruction->point.y);
                break;
                
            case CMD_LINE_TO:
                LaserDrawing_LineTo(instruction->point.x, instruction->point.y);
                break;
                
            case CMD_LASER_ON:
                LaserDrawing_SetLaser(true);
                break;
                
            case CMD_LASER_OFF:
                LaserDrawing_SetLaser(false);
                break;
                
            case CMD_DELAY:
                HAL_Delay(instruction->param);
                break;
                
            case CMD_SET_SPEED:
                LaserDrawing_SetSpeed(instruction->param);
                break;
        }
        
        gimbal_state.instruction_index++;
    } else {
        // 画图完成
        gimbal_state.is_drawing = false;
        LaserDrawing_SetLaser(false);
        Uart_Printf(&huart1, "Drawing completed\r\n");
    }
}

/**
 * @brief 开始画图案
 */
void LaserDrawing_StartPattern(const DrawingInstruction_t* pattern, uint16_t pattern_size_param)
{
    if (pattern == NULL || pattern_size_param == 0) {
        return;
    }
    
    current_pattern = pattern;
    pattern_size = pattern_size_param;
    gimbal_state.instruction_index = 0;
    gimbal_state.is_drawing = true;
    
    Uart_Printf(&huart1, "Starting drawing pattern with %d instructions\r\n", pattern_size);
}

/**
 * @brief 停止画图
 */
void LaserDrawing_Stop(void)
{
    gimbal_state.is_drawing = false;
    LaserDrawing_SetLaser(false);
    Stepper_Stop();
    Uart_Printf(&huart1, "Drawing stopped\r\n");
}

/**
 * @brief 移动到指定位置(激光关闭)
 */
void LaserDrawing_MoveTo(float x, float y)
{
    float pan_angle, tilt_angle;
    int32_t pan_steps, tilt_steps;
    
    // 坐标转换
    LaserDrawing_PointToAngle((Point_t){x, y}, &pan_angle, &tilt_angle);
    LaserDrawing_AngleToSteps(pan_angle, tilt_angle, &pan_steps, &tilt_steps);
    
    // 设置目标位置
    target_pan = pan_angle;
    target_tilt = tilt_angle;
    moving_to_target = true;
    
    // 执行移动
    Stepper_MoveToPosition(pan_steps, tilt_steps, DRAWING_SPEED_FAST);
}

/**
 * @brief 画线到指定位置(激光开启)
 */
void LaserDrawing_LineTo(float x, float y)
{
    float pan_angle, tilt_angle;
    int32_t pan_steps, tilt_steps;
    
    // 坐标转换
    LaserDrawing_PointToAngle((Point_t){x, y}, &pan_angle, &tilt_angle);
    LaserDrawing_AngleToSteps(pan_angle, tilt_angle, &pan_steps, &tilt_steps);
    
    // 设置目标位置
    target_pan = pan_angle;
    target_tilt = tilt_angle;
    moving_to_target = true;
    
    // 执行移动(较慢速度以确保画线质量)
    Stepper_MoveToPosition(pan_steps, tilt_steps, gimbal_state.speed);
}

/**
 * @brief 控制激光开关
 */
void LaserDrawing_SetLaser(bool state)
{
    gimbal_state.laser_on = state;
    
    // 控制激光引脚 - 假设使用PC13引脚
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, state ? GPIO_PIN_SET : GPIO_PIN_RESET);
}

/**
 * @brief 设置移动速度
 */
void LaserDrawing_SetSpeed(uint16_t speed)
{
    gimbal_state.speed = speed;
}

/**
 * @brief 回到原点
 */
void LaserDrawing_Home(void)
{
    LaserDrawing_SetLaser(false);
    LaserDrawing_MoveTo(0, 0);
}

/**
 * @brief 坐标点转换为角度
 */
void LaserDrawing_PointToAngle(Point_t point, float* pan_angle, float* tilt_angle)
{
    // 简单的线性映射，可根据实际需要调整
    *pan_angle = point.x;
    *tilt_angle = point.y;
    
    // 限制角度范围
    if (*pan_angle < PAN_MIN_ANGLE) *pan_angle = PAN_MIN_ANGLE;
    if (*pan_angle > PAN_MAX_ANGLE) *pan_angle = PAN_MAX_ANGLE;
    if (*tilt_angle < TILT_MIN_ANGLE) *tilt_angle = TILT_MIN_ANGLE;
    if (*tilt_angle > TILT_MAX_ANGLE) *tilt_angle = TILT_MAX_ANGLE;
}

/**
 * @brief 角度转换为步进电机步数
 */
void LaserDrawing_AngleToSteps(float pan_angle, float tilt_angle, int32_t* pan_steps, int32_t* tilt_steps)
{
    // 角度转步数：360度对应一圈的总步数
    *pan_steps = (int32_t)(pan_angle * TOTAL_STEPS_PER_REV / 360.0f);
    *tilt_steps = (int32_t)(tilt_angle * TOTAL_STEPS_PER_REV / 360.0f);
}

/**
 * @brief 查询是否正在画图
 */
bool LaserDrawing_IsDrawing(void)
{
    return gimbal_state.is_drawing;
}

/**
 * @brief 获取云台状态
 */
GimbalState_t LaserDrawing_GetState(void)
{
    return gimbal_state;
}
