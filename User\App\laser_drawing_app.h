#ifndef __LASER_DRAWING_APP_H__
#define __LASER_DRAWING_APP_H__

#include "MyDefine.h"

/* 激光画图系统配置 */
#define STEPS_PER_REVOLUTION    200     // 42步进电机每圈步数
#define MICROSTEPS              16      // 细分数
#define TOTAL_STEPS_PER_REV     (STEPS_PER_REVOLUTION * MICROSTEPS)

/* 云台运动范围 */
#define PAN_MIN_ANGLE           -90.0f  // 水平最小角度
#define PAN_MAX_ANGLE           90.0f   // 水平最大角度
#define TILT_MIN_ANGLE          -45.0f  // 垂直最小角度
#define TILT_MAX_ANGLE          45.0f   // 垂直最大角度

/* 画图参数 */
#define DRAWING_SPEED_DEFAULT   1000    // 默认画图速度(步/秒)
#define DRAWING_SPEED_FAST      2000    // 快速移动速度
#define DRAWING_SPEED_SLOW      500     // 慢速精细画图

/* 坐标系统 */
typedef struct {
    float x;    // X坐标 (对应水平角度)
    float y;    // Y坐标 (对应垂直角度)
} Point_t;

/* 画图命令类型 */
typedef enum {
    CMD_MOVE_TO = 0,    // 移动到指定位置(激光关闭)
    CMD_LINE_TO,        // 画线到指定位置(激光开启)
    CMD_LASER_ON,       // 开启激光
    CMD_LASER_OFF,      // 关闭激光
    CMD_DELAY,          // 延时
    CMD_SET_SPEED       // 设置速度
} DrawingCommand_t;

/* 画图指令结构体 */
typedef struct {
    DrawingCommand_t cmd;
    Point_t point;
    uint16_t param;     // 延时时间或速度参数
} DrawingInstruction_t;

/* 云台状态 */
typedef struct {
    float current_pan;      // 当前水平角度
    float current_tilt;     // 当前垂直角度
    bool laser_on;          // 激光状态
    uint16_t speed;         // 当前速度
    bool is_drawing;        // 是否正在画图
    uint16_t instruction_index; // 当前指令索引
} GimbalState_t;

/* 预定义图形大小 */
#define TRIANGLE_PATTERN_SIZE   8
#define SQUARE_PATTERN_SIZE     9
#define STAR_PATTERN_SIZE       10

/* 预定义图形 */
extern const DrawingInstruction_t triangle_pattern[];
extern const DrawingInstruction_t square_pattern[];
extern const DrawingInstruction_t circle_pattern[];
extern const DrawingInstruction_t star_pattern[];

/* 函数声明 */
void LaserDrawing_Init(void);
void LaserDrawing_Task(void);
void LaserDrawing_StartPattern(const DrawingInstruction_t* pattern, uint16_t pattern_size);
void LaserDrawing_Stop(void);
void LaserDrawing_MoveTo(float x, float y);
void LaserDrawing_LineTo(float x, float y);
void LaserDrawing_SetLaser(bool state);
void LaserDrawing_SetSpeed(uint16_t speed);
void LaserDrawing_Home(void);

/* 坐标转换函数 */
void LaserDrawing_PointToAngle(Point_t point, float* pan_angle, float* tilt_angle);
void LaserDrawing_AngleToSteps(float pan_angle, float tilt_angle, int32_t* pan_steps, int32_t* tilt_steps);

/* 状态查询函数 */
bool LaserDrawing_IsDrawing(void);
GimbalState_t LaserDrawing_GetState(void);

#endif
