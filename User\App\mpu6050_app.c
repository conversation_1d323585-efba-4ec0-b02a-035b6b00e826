#include "mpu6050_app.h"

float Pitch, Roll, Yaw; // pitch-�����ǣ���-��+����roll����ǣ�ǰ-��+����yaw-����ǣ���+��-����Ҫ���������ǵ�xyz�ῴ

// 串口输出控制变量
static uint32_t mpu_print_timer = 0;
static bool mpu_print_enable = false;

void Mpu6050_Init(void)
{
  MPU_Init();
	mpu_dmp_init();
}

void Mpu6050_Task(void)
{
  mpu_dmp_get_data(&Pitch, &Roll, &Yaw);
  Yaw = convert_to_continuous_yaw(Yaw);

  // 每500ms打印一次陀螺仪数据
  if(mpu_print_enable && (HAL_GetTick() - mpu_print_timer >= 500))
  {
    mpu_print_timer = HAL_GetTick();
    Uart_Printf(&huart1, "MPU6050: Pitch=%.2f, Roll=%.2f, Yaw=%.2f\r\n", Pitch, Roll, Yaw);
  }
}

/**
 * @brief 开启陀螺仪数据串口打印
 */
void Mpu6050_EnablePrint(void)
{
  mpu_print_enable = true;
  mpu_print_timer = HAL_GetTick();
  Uart_Printf(&huart1, "MPU6050 data printing enabled\r\n");
}

/**
 * @brief 关闭陀螺仪数据串口打印
 */
void Mpu6050_DisablePrint(void)
{
  mpu_print_enable = false;
  Uart_Printf(&huart1, "MPU6050 data printing disabled\r\n");
}

/**
 * @brief 打印一次当前陀螺仪数据
 */
void Mpu6050_PrintOnce(void)
{
  Uart_Printf(&huart1, "MPU6050 Current: Pitch=%.2f, Roll=%.2f, Yaw=%.2f\r\n", Pitch, Roll, Yaw);
}
