#include "mpu6050_app.h"

float Pitch, Roll, Yaw; // pitch-�����ǣ���-��+����roll����ǣ�ǰ-��+����yaw-����ǣ���+��-����Ҫ���������ǵ�xyz�ῴ

// 串口输出控制变量
static uint32_t mpu_print_timer = 0;
static bool mpu_print_enable = false;

void Mpu6050_Init(void)
{
  uint8_t mpu_init_result = MPU_Init();
  if(mpu_init_result == 0) {
    Uart_Printf(&huart1, "MPU6050 Init Success\r\n");
  } else {
    Uart_Printf(&huart1, "MPU6050 Init Failed, Error Code: %d\r\n", mpu_init_result);
  }

  uint8_t dmp_init_result = mpu_dmp_init();
  if(dmp_init_result == 0) {
    Uart_Printf(&huart1, "MPU6050 DMP Init Success\r\n");
  } else {
    Uart_Printf(&huart1, "MPU6050 DMP Init Failed, Error Code: %d\r\n", dmp_init_result);
  }
}

void Mpu6050_Task(void)
{
  uint8_t data_result = mpu_dmp_get_data(&Pitch, &Roll, &Yaw);

  if(data_result == 0) {
    Yaw = convert_to_continuous_yaw(Yaw);

    // 每500ms打印一次陀螺仪数据
    if(mpu_print_enable && (HAL_GetTick() - mpu_print_timer >= 500))
    {
      mpu_print_timer = HAL_GetTick();
      Uart_Printf(&huart1, "MPU6050: Pitch=%.2f, Roll=%.2f, Yaw=%.2f\r\n", Pitch, Roll, Yaw);
    }
  } else {
    // 数据读取失败时的处理
    static uint32_t error_print_timer = 0;
    if(HAL_GetTick() - error_print_timer >= 2000) {
      error_print_timer = HAL_GetTick();
      Uart_Printf(&huart1, "MPU6050 data read error: %d\r\n", data_result);
    }
  }
}

/**
 * @brief 开启陀螺仪数据串口打印
 */
void Mpu6050_EnablePrint(void)
{
  mpu_print_enable = true;
  mpu_print_timer = HAL_GetTick();
  Uart_Printf(&huart1, "MPU6050 data printing enabled\r\n");
}

/**
 * @brief 关闭陀螺仪数据串口打印
 */
void Mpu6050_DisablePrint(void)
{
  mpu_print_enable = false;
  Uart_Printf(&huart1, "MPU6050 data printing disabled\r\n");
}

/**
 * @brief 打印一次当前陀螺仪数据
 */
void Mpu6050_PrintOnce(void)
{
  Uart_Printf(&huart1, "MPU6050 Current: Pitch=%.2f, Roll=%.2f, Yaw=%.2f\r\n", Pitch, Roll, Yaw);
}

/**
 * @brief 读取并打印MPU6050原始数据
 */
void Mpu6050_PrintRawData(void)
{
  short accel[3], gyro[3];
  short temp;

  // 读取原始加速度和陀螺仪数据
  if(MPU_Get_Accelerometer(&accel[0], &accel[1], &accel[2]) == 0) {
    Uart_Printf(&huart1, "Accel: X=%d, Y=%d, Z=%d\r\n", accel[0], accel[1], accel[2]);
  } else {
    Uart_Printf(&huart1, "Failed to read accelerometer data\r\n");
  }

  if(MPU_Get_Gyroscope(&gyro[0], &gyro[1], &gyro[2]) == 0) {
    Uart_Printf(&huart1, "Gyro: X=%d, Y=%d, Z=%d\r\n", gyro[0], gyro[1], gyro[2]);
  } else {
    Uart_Printf(&huart1, "Failed to read gyroscope data\r\n");
  }

  // 修正温度读取函数调用
  temp = MPU_Get_Temperature();
  float temperature = 36.53f + ((float)temp) / 340.0f;
  Uart_Printf(&huart1, "Temperature: %.2f°C (Raw: %d)\r\n", temperature, temp);
}

/**
 * @brief 检查MPU6050连接状态
 */
void Mpu6050_CheckConnection(void)
{
  uint8_t who_am_i;

  // 读取WHO_AM_I寄存器
  who_am_i = MPU_Read_Byte(MPU_DEVICE_ID_REG);
  Uart_Printf(&huart1, "MPU6050 WHO_AM_I: 0x%02X ", who_am_i);

  if(who_am_i == MPU_ADDR) {
    Uart_Printf(&huart1, "(Connection OK)\r\n");
  } else {
    Uart_Printf(&huart1, "(Connection ERROR - Expected: 0x%02X)\r\n", MPU_ADDR);
  }
}
