#include "uart_app.h"
#include "laser_control_app.h"

extern uint8_t uart_rx_dma_buffer[BUFFER_SIZE]; // DMA ��ȡ������

extern uint8_t ring_buffer_input[BUFFER_SIZE]; // ���λ�������Ӧ����������
extern struct rt_ringbuffer ring_buffer; // ���λ�����

extern uint8_t uart_data_buffer[BUFFER_SIZE]; // ���ݴ���������

void Uart_Init(void)
{
  rt_ringbuffer_init(&ring_buffer, ring_buffer_input, BUFFER_SIZE);
  
  HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, BUFFER_SIZE); // ������ȡ�ж�
  __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT); // �ر� DMA ��"�����ж�"����
}

void Uart_Task(void)
{
  uint16_t uart_data_len = rt_ringbuffer_data_len(&ring_buffer);
  if(uart_data_len > 0)
  {
    rt_ringbuffer_get(&ring_buffer, uart_data_buffer, uart_data_len);
    uart_data_buffer[uart_data_len] = '\0';

    /* 移除换行符和回车符 */
    for(int i = 0; i < uart_data_len; i++) {
      if(uart_data_buffer[i] == '\r' || uart_data_buffer[i] == '\n') {
        uart_data_buffer[i] = '\0';
        break;
      }
    }

    /* 处理串口命令 */
    if(strlen((char*)uart_data_buffer) > 0) {
      // 处理陀螺仪相关命令
      if(strcmp((char*)uart_data_buffer, "mpu_on") == 0) {
        Mpu6050_EnablePrint();
      }
      else if(strcmp((char*)uart_data_buffer, "mpu_off") == 0) {
        Mpu6050_DisablePrint();
      }
      else if(strcmp((char*)uart_data_buffer, "mpu_read") == 0) {
        Mpu6050_PrintOnce();
      }
      else if(strcmp((char*)uart_data_buffer, "mpu_raw") == 0) {
        Mpu6050_PrintRawData();
      }
      else if(strcmp((char*)uart_data_buffer, "mpu_check") == 0) {
        Mpu6050_CheckConnection();
      }
      else if(strcmp((char*)uart_data_buffer, "help") == 0) {
        Uart_Printf(&huart1, "Available commands:\r\n");
        Uart_Printf(&huart1, "  mpu_on    - Enable MPU6050 continuous printing\r\n");
        Uart_Printf(&huart1, "  mpu_off   - Disable MPU6050 continuous printing\r\n");
        Uart_Printf(&huart1, "  mpu_read  - Read MPU6050 data once\r\n");
        Uart_Printf(&huart1, "  mpu_raw   - Read MPU6050 raw data\r\n");
        Uart_Printf(&huart1, "  mpu_check - Check MPU6050 connection\r\n");
        Uart_Printf(&huart1, "  help      - Show this help\r\n");
      }
      else {
        Uart_Printf(&huart1, "Unknown command: %s\r\n", uart_data_buffer);
        Uart_Printf(&huart1, "Type 'help' for available commands\r\n");
      }
    }

    memset(uart_data_buffer, 0, uart_data_len);
  }
}

