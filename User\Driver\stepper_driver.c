#include "stepper_driver.h"
#include "math.h"
#include "stdlib.h"

/* 全局变量 */
StepperControl_t stepper_pan;
StepperControl_t stepper_tilt;

/**
 * @brief 步进电机驱动初始化
 */
void Stepper_Init(void)
{
    // 初始化水平轴步进电机控制结构体
    stepper_pan.current_position = 0;
    stepper_pan.target_position = 0;
    stepper_pan.remaining_steps = 0;
    stepper_pan.current_speed = 0;
    stepper_pan.target_speed = 0;
    stepper_pan.state = STEPPER_IDLE;
    stepper_pan.direction = true;
    stepper_pan.last_step_time = 0;
    stepper_pan.step_interval = 0;
    
    // 初始化垂直轴步进电机控制结构体
    stepper_tilt.current_position = 0;
    stepper_tilt.target_position = 0;
    stepper_tilt.remaining_steps = 0;
    stepper_tilt.current_speed = 0;
    stepper_tilt.target_speed = 0;
    stepper_tilt.state = STEPPER_IDLE;
    stepper_tilt.direction = true;
    stepper_tilt.last_step_time = 0;
    stepper_tilt.step_interval = 0;
    
    // 使能步进电机
    Stepper_Enable(STEPPER_PAN, true);
    Stepper_Enable(STEPPER_TILT, true);
    
    Uart_Printf(&huart1, "Stepper motors initialized\r\n");
}

/**
 * @brief 步进电机任务 - 在调度器中定期调用
 */
void Stepper_Task(void)
{
    // 处理水平轴运动
    Stepper_ProcessMovement(&stepper_pan, STEPPER_PAN);
    
    // 处理垂直轴运动
    Stepper_ProcessMovement(&stepper_tilt, STEPPER_TILT);
}

/**
 * @brief 使能/禁用步进电机
 */
void Stepper_Enable(StepperAxis_t axis, bool enable)
{
    if (axis == STEPPER_PAN) {
        HAL_GPIO_WritePin(STEPPER_PAN_EN_PORT, STEPPER_PAN_EN_PIN, 
                         enable ? GPIO_PIN_RESET : GPIO_PIN_SET); // 低电平使能
    } else {
        HAL_GPIO_WritePin(STEPPER_TILT_EN_PORT, STEPPER_TILT_EN_PIN, 
                         enable ? GPIO_PIN_RESET : GPIO_PIN_SET);
    }
}

/**
 * @brief 设置步进电机方向
 */
void Stepper_SetDirection(StepperAxis_t axis, bool direction)
{
    if (axis == STEPPER_PAN) {
        HAL_GPIO_WritePin(STEPPER_PAN_DIR_PORT, STEPPER_PAN_DIR_PIN, 
                         direction ? GPIO_PIN_SET : GPIO_PIN_RESET);
        stepper_pan.direction = direction;
    } else {
        HAL_GPIO_WritePin(STEPPER_TILT_DIR_PORT, STEPPER_TILT_DIR_PIN, 
                         direction ? GPIO_PIN_SET : GPIO_PIN_RESET);
        stepper_tilt.direction = direction;
    }
}

/**
 * @brief 产生一个步进脉冲
 */
void Stepper_Step(StepperAxis_t axis)
{
    if (axis == STEPPER_PAN) {
        HAL_GPIO_WritePin(STEPPER_PAN_STEP_PORT, STEPPER_PAN_STEP_PIN, GPIO_PIN_SET);
        // 延时确保脉冲宽度
        for(volatile int i = 0; i < 100; i++);
        HAL_GPIO_WritePin(STEPPER_PAN_STEP_PORT, STEPPER_PAN_STEP_PIN, GPIO_PIN_RESET);
        
        // 更新位置
        if (stepper_pan.direction) {
            stepper_pan.current_position++;
        } else {
            stepper_pan.current_position--;
        }
    } else {
        HAL_GPIO_WritePin(STEPPER_TILT_STEP_PORT, STEPPER_TILT_STEP_PIN, GPIO_PIN_SET);
        // 延时确保脉冲宽度
        for(volatile int i = 0; i < 100; i++);
        HAL_GPIO_WritePin(STEPPER_TILT_STEP_PORT, STEPPER_TILT_STEP_PIN, GPIO_PIN_RESET);
        
        // 更新位置
        if (stepper_tilt.direction) {
            stepper_tilt.current_position++;
        } else {
            stepper_tilt.current_position--;
        }
    }
}

/**
 * @brief 移动指定步数
 */
void Stepper_MoveSteps(StepperAxis_t axis, int32_t steps, uint16_t speed)
{
    StepperControl_t* stepper = (axis == STEPPER_PAN) ? &stepper_pan : &stepper_tilt;
    
    if (steps == 0) return;
    
    // 设置方向
    bool direction = (steps > 0);
    Stepper_SetDirection(axis, direction);
    
    // 设置运动参数
    stepper->remaining_steps = abs(steps);
    stepper->target_speed = (speed > STEPPER_MAX_SPEED) ? STEPPER_MAX_SPEED : speed;
    stepper->current_speed = 0;
    stepper->state = STEPPER_ACCELERATING;
    stepper->target_position = stepper->current_position + steps;
    
    // 计算初始步进间隔
    if (stepper->target_speed > 0) {
        stepper->step_interval = 1000000 / stepper->target_speed; // 微秒
    }
}

/**
 * @brief 移动到指定位置
 */
void Stepper_MoveToPosition(int32_t pan_position, int32_t tilt_position, uint16_t speed)
{
    int32_t pan_steps = pan_position - stepper_pan.current_position;
    int32_t tilt_steps = tilt_position - stepper_tilt.current_position;
    
    Stepper_MoveSteps(STEPPER_PAN, pan_steps, speed);
    Stepper_MoveSteps(STEPPER_TILT, tilt_steps, speed);
}

/**
 * @brief 停止所有步进电机
 */
void Stepper_Stop(void)
{
    stepper_pan.state = STEPPER_IDLE;
    stepper_pan.remaining_steps = 0;
    stepper_pan.current_speed = 0;
    
    stepper_tilt.state = STEPPER_IDLE;
    stepper_tilt.remaining_steps = 0;
    stepper_tilt.current_speed = 0;
}

/**
 * @brief 回到原点
 */
void Stepper_Home(void)
{
    Stepper_MoveToPosition(0, 0, STEPPER_MAX_SPEED / 2);
}

/**
 * @brief 检查是否有步进电机在运动
 */
bool Stepper_IsMoving(void)
{
    return (stepper_pan.state != STEPPER_IDLE || stepper_tilt.state != STEPPER_IDLE);
}

/**
 * @brief 获取步进电机当前位置
 */
int32_t Stepper_GetPosition(StepperAxis_t axis)
{
    return (axis == STEPPER_PAN) ? stepper_pan.current_position : stepper_tilt.current_position;
}

/**
 * @brief 设置步进电机当前位置
 */
void Stepper_SetPosition(StepperAxis_t axis, int32_t position)
{
    if (axis == STEPPER_PAN) {
        stepper_pan.current_position = position;
    } else {
        stepper_tilt.current_position = position;
    }
}

/**
 * @brief 更新步进电机速度(加速度控制)
 */
static void Stepper_UpdateSpeed(StepperControl_t* stepper)
{
    if (stepper->state == STEPPER_ACCELERATING) {
        if (stepper->current_speed < stepper->target_speed) {
            stepper->current_speed += STEPPER_ACCELERATION / 1000; // 每毫秒增加的速度
            if (stepper->current_speed >= stepper->target_speed) {
                stepper->current_speed = stepper->target_speed;
                stepper->state = STEPPER_MOVING;
            }
        }
    } else if (stepper->state == STEPPER_DECELERATING) {
        if (stepper->current_speed > 100) { // 最小速度
            stepper->current_speed -= STEPPER_ACCELERATION / 1000;
        }
    }

    // 更新步进间隔
    if (stepper->current_speed > 0) {
        stepper->step_interval = 1000000 / stepper->current_speed; // 微秒
    }
}

/**
 * @brief 处理步进电机运动
 */
static void Stepper_ProcessMovement(StepperControl_t* stepper, StepperAxis_t axis)
{
    if (stepper->state == STEPPER_IDLE || stepper->remaining_steps == 0) {
        return;
    }

    uint32_t current_time = HAL_GetTick() * 1000; // 转换为微秒

    // 检查是否到了下一步的时间
    if (current_time - stepper->last_step_time >= stepper->step_interval) {
        // 执行步进
        Stepper_Step(axis);
        stepper->remaining_steps--;
        stepper->last_step_time = current_time;

        // 检查是否需要减速
        uint32_t decel_steps = (stepper->current_speed * stepper->current_speed) / (2 * STEPPER_ACCELERATION);
        if (stepper->remaining_steps <= decel_steps && stepper->state == STEPPER_MOVING) {
            stepper->state = STEPPER_DECELERATING;
        }

        // 检查是否完成运动
        if (stepper->remaining_steps == 0) {
            stepper->state = STEPPER_IDLE;
            stepper->current_speed = 0;
        } else {
            // 更新速度
            Stepper_UpdateSpeed(stepper);
        }
    }
}
