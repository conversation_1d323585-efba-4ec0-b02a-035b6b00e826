#ifndef __STEPPER_DRIVER_H__
#define __STEPPER_DRIVER_H__

#include "MyDefine.h"

/* 步进电机配置 */
#define STEPPER_PAN_STEP_PIN        GPIO_PIN_0   // 水平轴步进信号引脚
#define STEPPER_PAN_STEP_PORT       GPIOA
#define STEPPER_PAN_DIR_PIN         GPIO_PIN_1   // 水平轴方向信号引脚
#define STEPPER_PAN_DIR_PORT        GPIOA
#define STEPPER_PAN_EN_PIN          GPIO_PIN_2   // 水平轴使能信号引脚
#define STEPPER_PAN_EN_PORT         GPIOA

#define STEPPER_TILT_STEP_PIN       GPIO_PIN_3   // 垂直轴步进信号引脚
#define STEPPER_TILT_STEP_PORT      GPIOA
#define STEPPER_TILT_DIR_PIN        GPIO_PIN_4   // 垂直轴方向信号引脚
#define STEPPER_TILT_DIR_PORT       GPIOA
#define STEPPER_TILT_EN_PIN         GPIO_PIN_5   // 垂直轴使能信号引脚
#define STEPPER_TILT_EN_PORT        GPIOA

/* 步进电机参数 */
#define STEPPER_MIN_PULSE_WIDTH     2    // 最小脉冲宽度(微秒)
#define STEPPER_MAX_SPEED           5000 // 最大速度(步/秒)
#define STEPPER_ACCELERATION        1000 // 加速度(步/秒²)

/* 步进电机状态 */
typedef enum {
    STEPPER_IDLE = 0,
    STEPPER_MOVING,
    STEPPER_ACCELERATING,
    STEPPER_DECELERATING
} StepperState_t;

/* 步进电机轴 */
typedef enum {
    STEPPER_PAN = 0,    // 水平轴
    STEPPER_TILT        // 垂直轴
} StepperAxis_t;

/* 步进电机控制结构体 */
typedef struct {
    int32_t current_position;   // 当前位置(步数)
    int32_t target_position;    // 目标位置(步数)
    int32_t remaining_steps;    // 剩余步数
    uint16_t current_speed;     // 当前速度(步/秒)
    uint16_t target_speed;      // 目标速度(步/秒)
    StepperState_t state;       // 运动状态
    bool direction;             // 运动方向 (true=正向, false=反向)
    uint32_t last_step_time;    // 上次步进时间
    uint32_t step_interval;     // 步进间隔(微秒)
} StepperControl_t;

/* 全局变量声明 */
extern StepperControl_t stepper_pan;
extern StepperControl_t stepper_tilt;

/* 函数声明 */
void Stepper_Init(void);
void Stepper_Task(void);
void Stepper_Enable(StepperAxis_t axis, bool enable);
void Stepper_SetDirection(StepperAxis_t axis, bool direction);
void Stepper_Step(StepperAxis_t axis);
void Stepper_MoveSteps(StepperAxis_t axis, int32_t steps, uint16_t speed);
void Stepper_MoveToPosition(int32_t pan_position, int32_t tilt_position, uint16_t speed);
void Stepper_Stop(void);
void Stepper_Home(void);
bool Stepper_IsMoving(void);
int32_t Stepper_GetPosition(StepperAxis_t axis);
void Stepper_SetPosition(StepperAxis_t axis, int32_t position);

/* 内部函数 */
static void Stepper_UpdateSpeed(StepperControl_t* stepper);
static void Stepper_ProcessMovement(StepperControl_t* stepper, StepperAxis_t axis);

#endif
