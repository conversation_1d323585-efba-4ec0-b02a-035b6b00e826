#include "WouoUI_car_control.h"
#include "WouoUI.h"

// 任务名称数组
static const char* task_names[TASK_MAX] = {
    "Task1: A->B",
    "Task2: A->C->D->A", 
    "Task3: A->C->B->A",
    "Task4: Auto 4 Rounds"
};

// 位置名称数组
static const char* pos_names[] = {"A", "B", "C", "D"};

// 小车控制页面显示函数
static void CarControlPageShow(PageAddr page_addr) {
    CarControlPage *ccp = (CarControlPage*)page_addr;
    
    // 清空画布
    WouoUI_CanvasDrawRBox(&(p_cur_ui->w_all), 0, 0, WOUOUI_BUFF_WIDTH, WOUOUI_BUFF_HEIGHT, 1);
    
    // 绘制标题
    WouoUI_CanvasDrawStr(&(p_cur_ui->w_all), 5, 5, FONT_SIZE_12, "Car Control");
    
    // 绘制当前任务
    char task_str[32];
    sprintf(task_str, "Task: %s", task_names[ccp->current_task]);
    WouoUI_CanvasDrawStr(&(p_cur_ui->w_all), 5, 20, FONT_SIZE_8, task_str);
    
    // 绘制当前位置
    char pos_str[16];
    sprintf(pos_str, "Pos: %s", pos_names[ccp->current_pos]);
    WouoUI_CanvasDrawStr(&(p_cur_ui->w_all), 5, 35, FONT_SIZE_8, pos_str);
    
    // 绘制状态
    const char* state_str = (ccp->state == CAR_STATE_IDLE) ? "IDLE" :
                           (ccp->state == CAR_STATE_MOVING) ? "MOVING" : "DONE";
    WouoUI_CanvasDrawStr(&(p_cur_ui->w_all), 80, 35, FONT_SIZE_8, state_str);
    
    // 绘制时间
    if (ccp->state != CAR_STATE_IDLE) {
        char time_str[16];
        sprintf(time_str, "Time: %ds", ccp->elapsed_time);
        WouoUI_CanvasDrawStr(&(p_cur_ui->w_all), 5, 50, FONT_SIZE_8, time_str);
    }
    
    // 绘制轨迹图
    DrawCarTrack(ccp);
}

// 绘制小车轨迹图
static void DrawCarTrack(CarControlPage *ccp) {
    int16_t track_x = 10, track_y = 70;
    int16_t track_w = 100, track_h = 60;
    
    // 绘制轨迹框架
    WouoUI_CanvasDrawRFrame(&(p_cur_ui->w_all), track_x, track_y, track_w, track_h);
    
    // 绘制轨迹路径
    // 左半圆
    WouoUI_CanvasDrawCircle(&(p_cur_ui->w_all), track_x + 25, track_y + 30, 20, 0);
    // 右半圆  
    WouoUI_CanvasDrawCircle(&(p_cur_ui->w_all), track_x + 75, track_y + 30, 20, 0);
    
    // 绘制位置点
    DrawPositionPoint(track_x + 5, track_y + 30, CAR_POS_A, ccp->current_pos); // A点
    DrawPositionPoint(track_x + 95, track_y + 30, CAR_POS_B, ccp->current_pos); // B点
    DrawPositionPoint(track_x + 75, track_y + 10, CAR_POS_C, ccp->current_pos); // C点
    DrawPositionPoint(track_x + 75, track_y + 50, CAR_POS_D, ccp->current_pos); // D点
}

// 绘制位置点
static void DrawPositionPoint(int16_t x, int16_t y, CarPosition pos, CarPosition current) {
    // 当前位置用实心圆，其他用空心圆
    if (pos == current) {
        WouoUI_CanvasDrawDot(&(p_cur_ui->w_all), x, y, 3, 1);
    } else {
        WouoUI_CanvasDrawCircle(&(p_cur_ui->w_all), x, y, 2, 0);
    }
    
    // 绘制位置标签
    WouoUI_CanvasDrawStr(&(p_cur_ui->w_all), x-3, y-10, FONT_SIZE_8, pos_names[pos]);
}

// 小车控制页面响应函数
static bool CarControlPageReact(PageAddr page_addr) {
    CarControlPage *ccp = (CarControlPage*)page_addr;
    
    // 更新时间
    if (ccp->state == CAR_STATE_MOVING) {
        ccp->elapsed_time = (WouoUI_GetTime() - ccp->start_time) / 1000;
    }
    
    return true;
}

// 执行任务1: A→B
static void ExecuteTask1(CarControlPage *ccp) {
    ccp->current_task = TASK_1_A_TO_B;
    ccp->current_pos = CAR_POS_A;
    ccp->state = CAR_STATE_MOVING;
    ccp->start_time = WouoUI_GetTime();
    ccp->sound_light_enabled = true;
    
    // 模拟移动到B点
    // 实际应用中这里应该发送控制指令给小车
    SimulateCarMovement(ccp, CAR_POS_B, 15000); // 15秒到达B点
}

// 执行任务2: A→C→D→A
static void ExecuteTask2(CarControlPage *ccp) {
    ccp->current_task = TASK_2_A_TO_C_TO_D;
    ccp->current_pos = CAR_POS_A;
    ccp->state = CAR_STATE_MOVING;
    ccp->start_time = WouoUI_GetTime();
    ccp->sound_light_enabled = true;
    
    // 开始路径: A→C→D→A
    SimulateCarPath(ccp, (CarPosition[]){CAR_POS_C, CAR_POS_D, CAR_POS_A}, 3, 30000);
}

// 执行任务3: A→C→B→A  
static void ExecuteTask3(CarControlPage *ccp) {
    ccp->current_task = TASK_3_A_TO_C_TO_B;
    ccp->current_pos = CAR_POS_A;
    ccp->state = CAR_STATE_MOVING;
    ccp->start_time = WouoUI_GetTime();
    ccp->sound_light_enabled = true;
    
    // 开始路径: A→C→B→A
    SimulateCarPath(ccp, (CarPosition[]){CAR_POS_C, CAR_POS_B, CAR_POS_A}, 3, 40000);
}

// 执行任务4: 自动行驶4圈
static void ExecuteTask4(CarControlPage *ccp) {
    ccp->current_task = TASK_4_AUTO_4_ROUNDS;
    ccp->current_pos = CAR_POS_A;
    ccp->state = CAR_STATE_MOVING;
    ccp->start_time = WouoUI_GetTime();
    ccp->round_count = 0;
    ccp->sound_light_enabled = false;
    
    // 开始4圈自动行驶
    StartAutoRounds(ccp, 4);
}

// 小车控制页面初始化
void WouoUI_CarControlPageInit(CarControlPage *ccp, CallBackFunc call_back) {
    ccp->page.page_type = type_custom; // 自定义页面类型
    WouoUI_PageInit((PageAddr)ccp, call_back);
    
    // 设置页面方法
    static PageMethods car_methods = {
        .show = CarControlPageShow,
        .react = CarControlPageReact,
        .in = NULL,
        .in_para_init = NULL,
        .indicator_ctrl = NULL,
        .scrollbar_ctrl = NULL
    };
    ccp->page.methods = &car_methods;
    
    // 初始化状态
    ccp->current_pos = CAR_POS_A;
    ccp->state = CAR_STATE_IDLE;
    ccp->current_task = TASK_1_A_TO_B;
    ccp->round_count = 0;
    ccp->elapsed_time = 0;
    ccp->sound_light_enabled = false;
}

// 启动任务
void WouoUI_CarControlStartTask(CarControlPage *ccp, TaskType task) {
    switch(task) {
        case TASK_1_A_TO_B:
            ExecuteTask1(ccp);
            break;
        case TASK_2_A_TO_C_TO_D:
            ExecuteTask2(ccp);
            break;
        case TASK_3_A_TO_C_TO_B:
            ExecuteTask3(ccp);
            break;
        case TASK_4_AUTO_4_ROUNDS:
            ExecuteTask4(ccp);
            break;
    }
}