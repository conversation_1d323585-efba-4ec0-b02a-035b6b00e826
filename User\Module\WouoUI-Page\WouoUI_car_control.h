#ifndef WOUOUI_CAR_CONTROL_H
#define WOUOUI_CAR_CONTROL_H

#include "WouoUI_page.h"

// 小车位置枚举
typedef enum {
    CAR_POS_A = 0,
    CAR_POS_B,
    CAR_POS_C,
    CAR_POS_D
} CarPosition;

// 小车状态枚举
typedef enum {
    CAR_STATE_IDLE = 0,
    CAR_STATE_MOVING,
    CAR_STATE_COMPLETED
} CarState;

// 任务类型枚举
typedef enum {
    TASK_1_A_TO_B = 0,  // 任务1: A→B，声光提示
    TASK_2_A_TO_C_TO_D, // 任务2: A→C→D→A，声光提示
    TASK_3_A_TO_C_TO_B, // 任务3: A→C→B→A，声光提示
    TASK_4_AUTO_4_ROUNDS, // 任务4: 自动行驶4圈
    TASK_MAX
} TaskType;

// 小车控制页面结构
typedef struct {
    Page page;
    CarPosition current_pos;    // 当前位置
    CarState state;            // 当前状态
    TaskType current_task;     // 当前任务
    uint8_t round_count;       // 圈数计数
    uint32_t start_time;       // 开始时间
    uint32_t elapsed_time;     // 已用时间
    bool sound_light_enabled;  // 声光提示使能
} CarControlPage;

// 接口函数
void WouoUI_CarControlPageInit(CarControlPage *ccp, CallBackFunc call_back);
void WouoUI_CarControlStartTask(CarControlPage *ccp, TaskType task);
void WouoUI_CarControlUpdatePosition(CarControlPage *ccp, CarPosition pos);
void WouoUI_CarControlSetSoundLight(CarControlPage *ccp, bool enable);

#endif