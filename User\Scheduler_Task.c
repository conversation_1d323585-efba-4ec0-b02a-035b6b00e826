/*
 * 2024 ??爼 H ? - ???С??
 * ?ù?????STM32F407 ???劵?
 * ???·?°???
 *  ?????绺 * 
 *  TB6612z
 *  12V ?????鍊 *  ??? 8 ·??????÷?¨??????
 *  MPU6050
 *  0.96 ?砏led
 */

#include "Scheduler_Task.h"

void System_Init(void)
{
  Led_Init();
  Key_Init();
  Oled_Init();
  Uart_Init();
  Gray_Init();
  Motor_Init();
  Encoder_Init();
  Mpu6050_Init();
  PID_Init();
  LaserDrawing_Init();  // 初始化激光画图系统
  LaserControl_Init();  // 初始化激光控制系统
  Uart_Printf(&huart1, "=== System Init ===\r\n");
  HAL_TIM_Base_Start_IT(&htim2);
}

extern Encoder left_encoder;
extern Encoder right_encoder;

extern MOTOR left_motor;
extern MOTOR right_motor;

unsigned char measure_timer5ms;
unsigned char key_timer10ms;

unsigned char output_ff_flag;
unsigned int intput_timer500ms;

unsigned char intput_ff_flag;
unsigned int output_timer500ms;

unsigned int led_timer500ms; // ??????????LED ???? 500ms ?????

unsigned char point_count = 0; // ???????λ????????? + 1????? + 1??

unsigned char system_mode = 4; // ??????1 ~ 4 ??? 4 ???????

unsigned char circle_count = 0; // ????????????????

unsigned int distance = 0; // ???С??????????????

// TIM2 ?ж????????1ms ?ж??
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  if(htim->Instance != htim2.Instance) return;
 
  /* 10ms ???????????*/
  if(++key_timer10ms >= 10)
  {
    key_timer10ms = 0;
    Key_Task();
  }
  
  /* 5ms ????????*/
  if(++measure_timer5ms >= 5) 
  {
    measure_timer5ms = 0;
    Encoder_Task();
    distance += left_encoder.speed_cm_s;
    Mpu6050_Task();
    Gray_Task();
    PID_Task();
  }
  
  /* ????????? */
  if(Digtal != 0x00)
  {
    output_ff_flag = 1;
    if(++intput_timer500ms >= 800) intput_timer500ms = 800;
  }
  else if(output_ff_flag == 1 && intput_timer500ms == 800)
  {
    output_ff_flag = 0;
    intput_timer500ms = 0;
    point_count++;
    Car_State_Update();
  }
  
  /* ????????? */
  if(Digtal == 0x00)
  {
    intput_ff_flag = 1;
    if(++output_timer500ms >= 800) output_timer500ms = 800;
  }
  else if(intput_ff_flag == 1 && output_timer500ms == 800)
  {
    intput_ff_flag = 0;
    output_timer500ms = 0;
    point_count++;
    Car_State_Update();
  }
  
  /* LED ????? */
  if(led_state == 1 && ++led_timer500ms >= 500)
  {
    led_state = 0;
    led_timer500ms = 0;
  }
}

// ??ε?λ??????????????????????????С??????
void Car_State_Update(void)
{
  led_state = 1;
  distance = 0;
  
  switch(system_mode)
  {
    case 1: // ??????????? A -> B
      if(point_count == 1)
      {
        pid_running = 0;
        Motor_Brake(&left_motor);
        Motor_Brake(&right_motor);
      }
      break;
    case 2: // ??????????? A -> B -> C -> D
      if(point_count == 1)
        pid_control_mode = 1; // ????????????
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ??????????
        pid_set_target(&pid_angle, -176);
      }
      else if(point_count == 3)
        pid_control_mode = 1; // ????????????
      else if(point_count == 4)
      {
        pid_running = 0;
        Motor_Brake(&left_motor);
        Motor_Brake(&right_motor);
      }
      break;
    case 3: // ??????8 ??????? A -> C -> B -> D
      if(point_count == 1)
      {
        pid_control_mode = 1; // ????????????
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ??????????
        pid_set_target(&pid_angle, 253);
      }
      else if(point_count == 3)
      {
        pid_control_mode = 1; // ????????????
      }
      else if(point_count == 4)
      {
        pid_running = 0;
        Motor_Brake(&left_motor);
        Motor_Brake(&right_motor);
      }
      break;
    case 4: // ??????8 ????????
      if(point_count == 1)
      {
        pid_control_mode = 1; // ????????????
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ??????????
        pid_set_target(&pid_angle, 253 - (0.3 * circle_count)); // ?????????????????
      }
      else if(point_count == 3)
        pid_control_mode = 1; // ????????????
      else if(point_count == 4)
      {
        if(++circle_count >= 4)
        {
          pid_running = 0;
          Motor_Brake(&left_motor);
          Motor_Brake(&right_motor);
        }
        point_count = 0;
        pid_control_mode = 0; // ??????????
        pid_set_target(&pid_angle, 0 - (0.2 * circle_count)); // ?????????????????
      }
      break;
  }
  
  /* ?????????? */
  pid_reset(&pid_line);
  pid_reset(&pid_angle);
}
